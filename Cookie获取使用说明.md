# Cookie获取功能使用说明

## 功能概述
现在程序支持两种自动获取Cookie的模式：
- **基础模式**：仅访问页面，无需登录（推荐）
- **登录模式**：需要手动登录，获取完整Cookie

## 使用步骤

### 1. 启动程序
运行 `python 主程序.py` 启动闲鱼采集工具。

### 2. 选择Cookie获取模式
1. 在程序界面中，找到"Cookie设置"页面
2. 点击"刷新Cookie"按钮
3. 程序会弹出模式选择对话框

### 3. 基础模式（推荐）
**适用场景**：日常使用，快速获取基础Cookie

1. 选择"基础模式（推荐）"
2. 程序会自动启动Chrome浏览器
3. 浏览器会自动访问闲鱼网站并获取Cookie
4. 无需任何手动操作，浏览器会自动关闭
5. Cookie自动保存并应用

**优点**：
- 无需登录，操作简单
- 速度快，自动化程度高
- 获取的Cookie包含API调用所需的基础字段

### 4. 登录模式
**适用场景**：基础模式获取的Cookie无法正常使用时

1. 选择"登录模式"
2. 程序会自动启动Chrome浏览器
3. 浏览器会打开闲鱼网站
4. 在浏览器中手动登录您的淘宝/闲鱼账号
5. 登录完成后，在控制台输入 `y` 确认
6. 程序获取完整Cookie并保存
7. 浏览器自动关闭

### 5. 验证Cookie
程序会自动验证获取的Cookie是否包含必要的字段：
- `cookie2` - 基础认证字段
- `mtop_partitioned_detect` - 检测字段
- `_m_h5_tk` - API调用令牌（重要）

## 注意事项

### 系统要求
- 已安装 `playwright` 库
- 已安装 Chrome 浏览器驱动

### 安装依赖（如果需要）
```bash
pip install playwright
playwright install chromium
```

### 使用提示
1. **推荐使用基础模式**：大多数情况下基础模式就足够了
2. **网络连接**：确保网络连接正常，能够访问闲鱼网站
3. **模式选择**：如果基础模式获取的Cookie无法正常采集，再尝试登录模式
4. **取消操作**：可以随时点击"取消"按钮或在控制台输入 `n` 取消操作

### 常见问题

**Q: 浏览器无法启动？**
A: 请确保已安装playwright的浏览器驱动：`playwright install chromium`

**Q: 基础模式获取的Cookie无效？**
A: 尝试使用登录模式获取完整Cookie，或者稍后重试基础模式

**Q: 登录模式下程序卡住不动？**
A: 检查控制台是否有输入提示，需要手动输入 'y' 或 'n' 确认

**Q: 哪种模式更好？**
A: 推荐优先使用基础模式，速度快且通常有效。只有在基础模式无法正常采集时才使用登录模式。

## 技术细节

### Cookie获取范围
程序会获取以下域名的Cookie：
- `.taobao.com`
- `2.taobao.com`
- `.goofish.com`
- `h5api.m.goofish.com`

### 重要Cookie字段
程序特别关注以下重要Cookie字段：
- `_m_h5_tk` - API调用令牌
- `cookie2` - 用户认证
- `_tb_token_` - 淘宝令牌
- `sgcookie` - 安全Cookie
- `unb` - 用户标识
- `uc1`, `uc3`, `uc4` - 用户配置

## 安全说明
- Cookie信息会保存在本地配置文件中
- 请不要将Cookie信息分享给他人
- 定期更新Cookie以确保采集功能正常工作
